<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <meta name="prototype-page-id" content="assetsForm"> <!-- 添加此行指定当前页面ID -->
    <script src="annotationsData.js" defer></script>
    <script src="prototypeAnnotations.js" defer></script>

    <title>其他资产信息表单</title>
    <!-- 引入Font Awesome图标库 (国内CDN) -->
    <link rel="stylesheet" href="https://demo.axureux.com/fontawesome/5.7.2/pro/css/all.min.css">
    <!-- 引入ElementUI组件库 -->
    <link rel="stylesheet" href="assets/element-ui/index.css">
    <style>
        :root {
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #f5222d;
            --font-size-base: 14px;
            --heading-color: rgba(0, 0, 0, 0.85);
            --text-color: rgba(0, 0, 0, 0.65);
            --disabled-color: rgba(0, 0, 0, 0.25);
            --border-color-base: #d9d9d9;
            --box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            font-size: var(--font-size-base);
            color: var(--text-color);
            background-color: #f5f7fa;
            line-height: 1.5;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 15px;
        }

        .page-header {
            margin-bottom: 20px;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color-base);
        }

        .page-title {
            font-size: 24px;
            color: var(--heading-color);
            font-weight: 500;
        }

        .form-card {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: var(--box-shadow-base);
            margin-bottom: 24px;
        }

        .form-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-base);
            background-color: #fafafa;
            border-radius: 4px 4px 0 0;
        }

        .form-card-title {
            font-size: 16px;
            color: var(--heading-color);
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .form-card-title i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .form-card-body {
            padding: 24px;
        }

        .form-footer {
            text-align: center;
            padding: 24px 0;
        }

        .required:before {
            content: "*";
            color: var(--error-color);
            margin-right: 4px;
        }

        /* 自定义ElementUI样式 */
        .el-form-item {
            margin-bottom: 22px;
        }

        .el-textarea__inner {
            min-height: 120px !important;
        }

        .el-form-item__label {
            font-weight: 500;
        }

        .el-button--primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .el-button--primary:hover,
        .el-button--primary:focus {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        .el-date-editor.el-input {
            width: 100%;
        }

        .el-select {
            width: 100%;
        }

        .el-input-number {
            width: 100%;
        }

        .el-checkbox-group {
            display: flex;
            flex-wrap: wrap;
        }

        .el-checkbox {
            margin-right: 15px;
            margin-bottom: 5px;
        }

        /* 提示图标样式 */
        .tooltip-icon {
            color: #909399;
            margin-left: 5px;
            cursor: pointer;
        }

        /* 去除Vue初始化闪烁 */
        [v-cloak] {
            display: none;
        }

        /* 帮助文本样式 */
        .help-text {
            font-size: 12px;
            color: #909399;
            line-height: 1.5;
            margin-top: 5px;
        }
        
        /* 级联选择器样式 */
        .el-cascader {
            width: 100%;
        }

        /* 标签区分 */
        .form-section-title {
            font-size: 15px;
            color: var(--heading-color);
            font-weight: 500;
            margin: 15px 0;
            padding-bottom: 10px;
            border-bottom: 1px dashed #e8e8e8;
        }
    </style>
</head>

<body>
    <div id="app" v-cloak>
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">其他资产信息表单</h1>
            </div>

            <el-form :model="formData" :rules="rules" ref="assetsForm" label-width="150px" size="small">
                <!-- 基本信息 -->
                <div class="form-card">
                    <div class="form-card-header">
                        <h2 class="form-card-title">
                            <i class="fas fa-info-circle"></i> 基本信息
                        </h2>
                    </div>
                    <div class="form-card-body">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="code">
                                    <template slot="label">
                                        <span>资产编号</span>
                                    </template>
                                    <el-input v-model="formData.code" placeholder="保存后系统自动生成" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="enterpriseCode">
                                    <template slot="label">
                                        <span>企业自定义编号</span>
                                    </template>
                                    <el-input v-model="formData.enterpriseCode" placeholder="请输入企业自定义编号"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="name">
                                    <template slot="label">
                                        <span>资产名称</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('系统内要求资产名称唯一')"></i>
                                    </template>
                                    <el-input v-model="formData.name" placeholder="请输入资产名称"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="groupName">
                                    <template slot="label">
                                        <span>所属集团</span>
                                    </template>
                                    <el-select v-model="formData.groupName" placeholder="请选择所属集团" disabled>
                                        <el-option :key="0" label="厦门市城市建设发展投资有限公司" :value="0"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="companyName">
                                    <template slot="label">
                                        <span>所属企业</span>
                                    </template>
                                    <el-select v-model="formData.companyName" placeholder="请选择所属企业">
                                        <el-option :key="0" label="厦门市城市建设发展投资有限公司" :value="0"></el-option>
                                        <el-option :key="1" label="厦门市地热资源管理有限公司" :value="1"></el-option>
                                        <el-option :key="2" label="厦门兴地房屋征迁服务有限公司" :value="2"></el-option>
                                        <el-option :key="3" label="厦门地丰置业有限公司" :value="3"></el-option>
                                        <el-option :key="4" label="图智策划咨询（厦门）有限公司" :value="4"></el-option>
                                        <el-option :key="5" label="厦门市集众祥和物业管理有限公司" :value="5"></el-option>
                                        <el-option :key="6" label="厦门市人居乐业物业服务有限公司" :value="6"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="region">
                                    <template slot="label">
                                        <span>资产位置</span>
                                    </template>
                                    <el-cascader
                                        v-model="formData.region"
                                        :options="regionOptions"
                                        @change="handleRegionChange"
                                        placeholder="请选择省/市/区">
                                    </el-cascader>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="address">
                                    <template slot="label">
                                        <span>详细地址</span>
                                    </template>
                                    <el-input v-model="formData.address" placeholder="请输入详细地址"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="status">
                                    <template slot="label">
                                        <span>状态</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('备案数据支持撤回、草稿数据和撤回数据支持作废')"></i>
                                    </template>
                                    <el-select v-model="formData.status" placeholder="请选择状态">
                                        <el-option :key="0" label="草稿" :value="0" :disabled="isDraftDisabled"></el-option>
                                        <el-option :key="1" label="备案" :value="1" :disabled="isFiledDisabled"></el-option>
                                        <el-option :key="2" label="撤回" :value="2" :disabled="isRevokeDisabled"></el-option>
                                        <el-option :key="4" label="作废" :value="4" :disabled="isInvalidateDisabled"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="manageUnit">
                                    <template slot="label">
                                        <span>管理单位</span>
                                    </template>
                                    <el-select v-model="formData.manageUnit" placeholder="请选择管理单位">
                                        <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                    <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="reportOrNot">
                                    <template slot="label">
                                        <span>是否报送国资委</span>
                                    </template>
                                    <el-select v-model="formData.reportOrNot" placeholder="请选择">
                                        <el-option label="否" :value="0"></el-option>
                                        <el-option label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="operator">
                                    <template slot="label">
                                        <span>经办人</span>
                                    </template>
                                    <el-input v-model="formData.operator" placeholder="请输入经办人"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="entryClerk">
                                    <template slot="label">
                                        <span>录入人</span>
                                    </template>
                                    <el-input v-model="formData.entryClerk" placeholder="录入人" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="createTime">
                                    <template slot="label">
                                        <span>录入时间</span>
                                    </template>
                                    <el-input v-model="formData.createTime" placeholder="录入时间" disabled></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>

                <!-- 资产情况 -->
                <div class="form-card">
                    <div class="form-card-header">
                        <h2 class="form-card-title">
                            <i class="fas fa-building"></i> 资产情况
                        </h2>
                    </div>
                    <div class="form-card-body">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="assetsStatus">
                                    <template slot="label">
                                        <span>资产使用状态</span>
                                    </template>
                                    <el-select
                                        v-model="formData.assetsStatus"
                                        multiple
                                        placeholder="请选择资产使用状态">
                                        <el-option :key="0" label="闲置" :value="0"></el-option>
                                        <el-option :key="1" label="自用" :value="1"></el-option>
                                        <el-option :key="2" label="出租" :value="2"></el-option>
                                        <el-option :key="3" label="出借" :value="3"></el-option>
                                        <el-option :key="4" label="占用" :value="4"></el-option>
                                        <el-option :key="5" label="欠租" :value="5"></el-option>
                                        <el-option :key="6" label="转让" :value="6"></el-option>
                                        <el-option :key="7" label="其他" :value="7"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="gainDate">
                                    <template slot="label">
                                        <span>资产取得日期</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.gainDate"
                                        type="date"
                                        placeholder="请选择资产取得日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="dateOptions"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="assetEntryDate">
                                    <template slot="label">
                                        <span>资产入账日期</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.assetEntryDate"
                                        type="date"
                                        placeholder="请选择资产入账日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="dateOptions"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="assetsAmount">
                                    <template slot="label">
                                        <span>资产原值（元）</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.assetsAmount" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入资产原值"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="bookAmount">
                                    <template slot="label">
                                        <span>账面价值（元）</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.bookAmount" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入账面价值"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="dateOfBookValue">
                                    <template slot="label">
                                        <span>账面价值时点</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.dateOfBookValue"
                                        type="date"
                                        placeholder="请选择账面价值时点"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="dateOptions"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item prop="remark">
                                    <template slot="label">
                                        <span>备注</span>
                                    </template>
                                    <el-input 
                                        type="textarea" 
                                        v-model="formData.remark" 
                                        placeholder="请输入备注" 
                                        :rows="4">
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                
                <!-- 表单提交按钮 -->
                <div class="form-footer">
                    <el-button @click="resetForm">重置</el-button>
                    <el-button type="primary" @click="submitForm">提交</el-button>
                </div>
            </el-form>
        </div>
    </div>

    <!-- 引入Vue.js -->
    <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.14/vue.min.js"></script>
    <!-- 引入ElementUI组件库 -->
    <script src="assets/element-ui/index.js"></script>
    <!-- 引入axios -->
    <script src="https://cdn.bootcdn.net/ajax/libs/axios/0.21.1/axios.min.js"></script>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    isEditMode: false,
                    originalStatus: null,
                    currentUser: '张三', // 模拟当前登录用户
                    enterpriseOptions: [ // 从 enterpriseList.html 获取的企业信息
                        { value: 0, label: '厦门市城市建设发展投资有限公司' },
                        { value: 1, label: '厦门市地热资源管理有限公司' },
                        { value: 2, label: '厦门兴地房屋征迁服务有限公司' },
                        { value: 3, label: '厦门地丰置业有限公司' },
                        { value: 4, label: '图智策划咨询（厦门）有限公司' },
                        { value: 5, label: '厦门市集众祥和物业管理有限公司' },
                        { value: 6, label: '厦门市人居乐业物业服务有限公司' }
                    ],
                    // 表单数据
                    formData: {
                        // 基本信息
                        code: '', // 资产编号
                        enterpriseCode: '', // 企业自定义编号
                        name: '', // 资产名称
                        groupName: 0, // 所属集团，默认为"厦门市城市建设发展投资有限公司"
                        companyName: '', // 所属企业
                        region: [], // 省市区
                        province: '', // 省
                        city: '', // 市
                        area: '', // 区
                        address: '', // 详细地址
                        status: 0, // 状态，默认为"草稿"
                        manageUnit: '', // 管理单位
                        reportOrNot: '', // 是否报送国资委
                        operator: '', // 经办人
                        entryClerk: '', // 录入人
                        createTime: '', // 录入时间
                        
                        // 资产情况
                        assetsStatus: [], // 资产使用状态
                        gainDate: '', // 资产取得日期
                        assetEntryDate: '', // 资产入账日期
                        assetsAmount: '', // 资产原值（元）
                        bookAmount: '', // 账面价值（元）
                        dateOfBookValue: '', // 账面价值时点
                        remark: '', // 备注
                    },
                    
                    // 省市区数据
                    regionOptions: [
                        {
                            value: '福建省',
                            label: '福建省',
                            children: [
                                {
                                    value: '厦门市',
                                    label: '厦门市',
                                    children: [
                                        { value: '思明区', label: '思明区' },
                                        { value: '湖里区', label: '湖里区' },
                                        { value: '集美区', label: '集美区' },
                                        { value: '海沧区', label: '海沧区' },
                                        { value: '同安区', label: '同安区' },
                                        { value: '翔安区', label: '翔安区' }
                                    ]
                                },
                                {
                                    value: '福州市',
                                    label: '福州市',
                                    children: [
                                        { value: '鼓楼区', label: '鼓楼区' },
                                        { value: '台江区', label: '台江区' },
                                        { value: '仓山区', label: '仓山区' },
                                        { value: '马尾区', label: '马尾区' },
                                        { value: '晋安区', label: '晋安区' }
                                    ]
                                }
                            ]
                        },
                        {
                            value: '浙江省',
                            label: '浙江省',
                            children: [
                                {
                                    value: '杭州市',
                                    label: '杭州市',
                                    children: [
                                        { value: '上城区', label: '上城区' },
                                        { value: '下城区', label: '下城区' },
                                        { value: '江干区', label: '江干区' },
                                        { value: '拱墅区', label: '拱墅区' },
                                        { value: '西湖区', label: '西湖区' }
                                    ]
                                }
                            ]
                        }
                    ],
                    
                    // 日期选择器配置
                    dateOptions: {
                        disabledDate(time) {
                            return time.getTime() > Date.now();
                        }
                    },
                    
                    // 表单验证规则
                    rules: {
                        // 基本信息验证规则
                        code: [
                            { required: true, message: '资产编号不能为空', trigger: 'blur' }
                        ],
                        name: [
                            { required: true, message: '请输入资产名称', trigger: 'blur' }
                        ],
                        groupName: [
                            { required: true, message: '请选择所属集团', trigger: 'change' }
                        ],
                        companyName: [
                            { required: true, message: '请选择所属企业', trigger: 'change' }
                        ],
                        region: [
                            { required: true, message: '请选择资产位置', trigger: 'change' }
                        ],
                        status: [
                            { required: true, message: '请选择状态', trigger: 'change' }
                        ],
                        manageUnit: [
                            { required: true, message: '请选择管理单位', trigger: 'change' }
                        ],
                        reportOrNot: [
                            { required: true, message: '请选择是否报送国资委', trigger: 'change' }
                        ],
                        operator: [
                            { required: true, message: '请输入经办人', trigger: 'blur' }
                        ],
                        entryClerk: [
                            { required: true, message: '录入人不能为空', trigger: 'blur' }
                        ],
                        createTime: [
                            { required: true, message: '录入时间不能为空', trigger: 'blur' }
                        ],
                        assetsStatus: [
                            { required: true, message: '请选择资产使用状态', trigger: 'change' }
                        ],
                        gainDate: [
                            { required: true, message: '请选择资产取得日期', trigger: 'change' }
                        ],
                        assetsAmount: [
                            { required: true, message: '请输入资产原值', trigger: 'blur' }
                        ]
                    }
                };
            },
            computed: {
                isDraftDisabled() {
                    if (!this.isEditMode) return false; // 新建时，草稿可选
                    // 编辑时，只有"备案"状态(1)不能退回"草稿"
                    return this.originalStatus === 1;
                },
                isFiledDisabled() {
                    if (!this.isEditMode) return false; // 新建时，备案可选
                    // 编辑时，只有"草稿"状态(0)可以提交"备案"
                    return this.originalStatus !== 0;
                },
                isRevokeDisabled() {
                    // "撤回"选项只有在编辑模式且原状态为"备案"(1)时才可用
                    return !this.isEditMode || this.originalStatus !== 1;
                },
                isInvalidateDisabled() {
                    // "作废"选项只有在编辑模式且原状态为"草稿"(0)或"撤回"(2)时才可用
                    return !this.isEditMode || (this.originalStatus !== 0 && this.originalStatus !== 2);
                }
            },
            methods: {
                // 区域选择变更处理
                handleRegionChange(value) {
                    if (value && value.length === 3) {
                        this.formData.province = value[0];
                        this.formData.city = value[1];
                        this.formData.area = value[2];
                    }
                },
                
                // 显示提示信息
                showTip(message) {
                    this.$message({
                        message: message,
                        type: 'info',
                        showClose: true,
                        duration: 5000
                    });
                },
                
                // 重置表单
                resetForm() {
                    this.$confirm('确定要重置表单吗？所有已填写的数据将会丢失。', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$refs.assetsForm.resetFields();
                        // 恢复默认值
                        this.formData.groupName = 0;
                        this.formData.status = 0;
                        
                        this.$message({
                            type: 'success',
                            message: '表单已重置！'
                        });
                    }).catch(() => {
                        // 取消重置操作
                    });
                },
                
                // 提交表单
                submitForm() {
                    this.$refs.assetsForm.validate((valid) => {
                        if (valid) {
                            // 验证是否有选择省市区
                            if (!this.formData.region || this.formData.region.length < 3) {
                                this.$message.error('请选择完整的省/市/区');
                                return;
                            }
                            
                            // 准备提交数据
                            this.$message({
                                type: 'info',
                                message: '正在提交数据...'
                            });
                            
                            // 根据状态处理不同的提交逻辑
                            const statusMap = {
                                0: '草稿',
                                1: '备案',
                                2: '撤回',
                                4: '作废'
                            };
                            
                            // 模拟API调用
                            setTimeout(() => {
                                console.log('提交的表单数据：', this.formData);
                                
                                // 模拟成功提交并生成编码
                                const randomCode = 'O' + new Date().getFullYear() + Math.floor(Math.random() * 10000).toString().padStart(4, '0');
                                this.formData.code = randomCode;
                                
                                // 模拟成功提交
                                this.$message({
                                    type: 'success',
                                    message: `${statusMap[this.formData.status]}保存成功！资产编号：${this.formData.code}`
                                });
                                
                                // 如果是备案，则禁用表单编辑
                                if (this.formData.status === 1) {
                                    // 实际项目中可能还需要重新加载表单或跳转页面
                                }
                            }, 1000);
                        } else {
                            this.$message({
                                type: 'error',
                                message: '表单验证失败，请检查并完善表单信息！'
                            });
                            return false;
                        }
                    });
                },
                
                // 格式化日期
                formatDate(date) {
                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');
                    const seconds = date.getSeconds().toString().padStart(2, '0');
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                },
                
                // 设置用户和时间默认值
                setDefaultUserInfo() {
                    this.formData.operator = this.currentUser;
                    this.formData.entryClerk = this.currentUser;
                    this.formData.createTime = this.formatDate(new Date());
                },

                // 加载编辑数据
                loadEditData() {
                    const data = localStorage.getItem('editAssetsData');
                    if (data) {
                        try {
                            const editData = JSON.parse(data);
                            this.formData = Object.assign(this.formData, editData);
                            
                            // 重构区域数组以适应级联选择器
                            if (editData.province && editData.city && editData.area) {
                                this.formData.region = [editData.province, editData.city, editData.area];
                            } else {
                                this.formData.region = [];
                            }
                            
                            this.originalStatus = editData.status;

                        } catch (e) {
                            console.error('从localStorage解析编辑数据失败', e);
                            this.$message.error('加载编辑数据失败！');
                            this.isEditMode = false;
                            this.setDefaultUserInfo();
                        }
                    } else {
                        this.$message.warning('未找到编辑数据，将以新增模式打开。');
                        this.isEditMode = false;
                        this.setDefaultUserInfo();
                    }
                }
            },
            mounted() {
                // 页面加载时自动为所属集团设置默认值
                this.formData.groupName = 0;
                
                const urlParams = new URLSearchParams(window.location.search);
                const assetId = urlParams.get('id');
                
                if (assetId) {
                    this.isEditMode = true;
                    this.loadEditData();
                } else {
                    this.isEditMode = false;
                    this.setDefaultUserInfo();
                }
            }
        });
    </script>
</body>
</html>
