<template>
  <div class="other-assets-form">
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
            基本信息
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerBasicForm" :schemas="basicInfoSchema" />
        </div>
      </div>

      <!-- 资产情况 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:building-outlined" class="title-icon" />
            资产情况
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerAssetsForm" :schemas="assetsInfoSchema" />
        </div>
      </div>

      <!-- 表单操作按钮 -->
      <div class="form-footer">
        <a-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </a-button>
        <a-button @click="handleReset" style="margin-left: 12px"> 重置 </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="OtherAssetsForm" setup>
  import { ref, onMounted, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicForm, useForm } from '/@/components/Form';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { basicInfoSchema, assetsInfoSchema } from './otherForm.data';
  import { saveOrUpdate, getDetail } from './other.api';

  const route = useRoute();
  const router = useRouter();
  const { createMessage } = useMessage();
  const loading = ref(false);

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');

  // 表单数据
  const _formData = ref<any>({});

  // 注册表单
  const [registerBasicForm, { resetFields: resetBasicFields, setFieldsValue: setBasicFieldsValue, validate: validateBasic }] = useForm({
    labelWidth: 140,
    baseColProps: { span: 8 },
    showActionButtonGroup: false,
    schemas: basicInfoSchema,
  });

  const [registerAssetsForm, { resetFields: resetAssetsFields, setFieldsValue: setAssetsFieldsValue, validate: validateAssets }] = useForm({
    labelWidth: 140,
    baseColProps: { span: 8 },
    showActionButtonGroup: false,
    schemas: assetsInfoSchema,
  });

  // 获取标题
  const _getTitle = computed(() => (!isUpdate.value ? '新增其他资产信息' : '编辑其他资产信息'));

  // 初始化
  onMounted(async () => {
    // 判断是新增还是编辑
    const path = route.path;
    if (path.includes('/edit/')) {
      isUpdate.value = true;
      recordId.value = route.params.id as string;
      await loadData();
    } else {
      isUpdate.value = false;
      resetFields();
      // 设置默认值
      setDefaultValues();
    }
  });

  // 设置默认值
  function setDefaultValues() {
    setBasicFieldsValue({
      groupName: 0, // 默认所属集团
      status: 0, // 默认草稿状态
      entryClerk: '当前用户', // 录入人
      createTime: new Date().toISOString().split('T')[0], // 录入时间
    });
  }

  // 加载数据
  async function loadData() {
    try {
      const record = await getDetail(recordId.value);
      setFieldsValue(record);
    } catch (error) {
      createMessage.error('加载数据失败');
    }
  }

  // 设置表单值
  function setFieldsValue(data: any) {
    // 基本信息
    setBasicFieldsValue({
      code: data.code,
      enterpriseCode: data.enterpriseCode,
      name: data.name,
      groupName: data.groupName,
      companyName: data.companyName,
      region: [data.province, data.city, data.area],
      address: data.address,
      status: data.status,
      manageUnit: data.manageUnit,
      reportOrNot: data.reportOrNot,
      operator: data.operator,
      entryClerk: data.entryClerk,
      createTime: data.createTime,
    });

    // 资产情况
    setAssetsFieldsValue({
      assetsStatus: data.assetsStatus,
      gainDate: data.gainDate,
      assetEntryDate: data.assetEntryDate,
      assetsAmount: data.assetsAmount,
      bookAmount: data.bookAmount,
      dateOfBookValue: data.dateOfBookValue,
      remark: data.remark,
    });
  }

  // 重置表单
  function resetFields() {
    resetBasicFields();
    resetAssetsFields();
  }

  // 提交表单
  async function handleSubmit() {
    try {
      loading.value = true;
      
      // 验证所有表单
      const [basicData, assetsData] = await Promise.all([validateBasic(), validateAssets()]);

      // 合并数据
      const formData = {
        ...basicData,
        ...assetsData,
        // 处理地区数据
        province: basicData.region?.[0] || '',
        city: basicData.region?.[1] || '',
        area: basicData.region?.[2] || '',
      };

      // 如果是编辑模式，添加ID
      if (isUpdate.value) {
        formData.id = recordId.value;
      }

      await saveOrUpdate(formData, isUpdate.value);
      createMessage.success(isUpdate.value ? '编辑成功' : '新增成功');
      router.push('/assetsInfo/other');
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 重置
  function handleReset() {
    resetFields();
    if (!isUpdate.value) {
      setDefaultValues();
    }
  }
</script>

<style lang="less" scoped>
  .other-assets-form {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 16px;

    .simple-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      padding: 16px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-card {
      background: white;
      border-radius: 6px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .form-card-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .form-card-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }

      .form-card-body {
        padding: 20px;
      }
    }

    .form-footer {
      background: white;
      padding: 16px 20px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      text-align: center;
    }
  }
</style> 