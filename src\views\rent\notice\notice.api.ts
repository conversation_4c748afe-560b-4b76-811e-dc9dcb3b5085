import { defHttp } from '/@/utils/http/axios';

enum Api {
  NoticeList = '/rent/notice/queryPageList',
  NoticeDetail = '/mock/notice/detail',
  NoticeAdd = '/mock/notice/add',
  NoticeUpdate = '/mock/notice/update',
  NoticeDelete = '/mock/notice/delete',
  LinkAssets = '/mock/notice/linkAssets',
  AssetOptions = '/mock/notice/assetOptions',
}

/**
 * 获取招租公告列表
 * @param params 查询参数
 */
export const getNoticeList = (params) => {
  return defHttp.get({
    url: Api.NoticeList,
    params,
  });
};

/**
 * 获取招租公告详情
 * @param id 公告ID
 */
export const getNoticeDetail = (id: string | number) => {
  return defHttp.get({
    url: Api.NoticeDetail,
    params: { id },
  });
};

/**
 * 新增招租公告
 * @param params 公告数据
 */
export const addNotice = (params) => {
  return defHttp.post({
    url: Api.NoticeAdd,
    params,
  });
};

/**
 * 更新招租公告
 * @param params 公告数据
 */
export const updateNotice = (params) => {
  return defHttp.put({
    url: Api.NoticeUpdate,
    params,
  });
};

/**
 * 删除招租公告
 * @param id 公告ID
 */
export const deleteNotice = (id: string | number) => {
  return defHttp.delete({
    url: Api.NoticeDelete,
    params: { id },
  });
};

/**
 * 关联资产包
 * @param params 关联参数
 */
export const linkAssets = (params) => {
  return defHttp.post({
    url: Api.LinkAssets,
    params,
  });
};

/**
 * 获取资产包选项
 * @param params 查询参数
 */
export const getAssetOptions = (params) => {
  return defHttp.get({
    url: Api.AssetOptions,
    params,
  });
};